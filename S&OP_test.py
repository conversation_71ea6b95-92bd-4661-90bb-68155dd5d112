import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class SAndOPModel:
    def __init__(self):
        # 初始化数据存储
        self.price_forecasts = pd.DataFrame(columns=['Period', 'Commodity', 'Price'])
        self.production_plans = pd.DataFrame(columns=['Period', 'Mine', 'Quantity'])
        self.demand_forecasts = pd.DataFrame(columns=['Period', 'Region', 'Quantity'])
        self.logistics_data = pd.DataFrame(columns=['Period', 'Route', 'Cost', 'Capacity'])
        self.sales_plan = pd.DataFrame()
        
    def add_price_forecast(self, period, commodity, price):
        """添加价格预测数据"""
        new_data = pd.DataFrame([[period, commodity, price]], 
                               columns=['Period', 'Commodity', 'Price'])
        self.price_forecasts = pd.concat([self.price_forecasts, new_data], ignore_index=True)
        
    def add_production_plan(self, period, mine, quantity):
        """添加生产计划数据"""
        new_data = pd.DataFrame([[period, mine, quantity]], 
                               columns=['Period', 'Mine', 'Quantity'])
        self.production_plans = pd.concat([self.production_plans, new_data], ignore_index=True)
        
    def add_demand_forecast(self, period, region, quantity):
        """添加需求预测数据"""
        new_data = pd.DataFrame([[period, region, quantity]], 
                               columns=['Period', 'Region', 'Quantity'])
        self.demand_forecasts = pd.concat([self.demand_forecasts, new_data], ignore_index=True)
        
    def add_logistics_data(self, period, route, cost, capacity):
        """添加物流数据"""
        new_data = pd.DataFrame([[period, route, cost, capacity]], 
                               columns=['Period', 'Route', 'Cost', 'Capacity'])
        self.logistics_data = pd.concat([self.logistics_data, new_data], ignore_index=True)
        
    def generate_sales_plan(self, target_month):
        """
        生成月度销售计划
        基于利润最大化原则，考虑生产和需求约束
        """
        # 筛选目标月份数据
        price_data = self.price_forecasts[self.price_forecasts['Period'] == target_month]
        production_data = self.production_plans[self.production_plans['Period'] == target_month]
        demand_data = self.demand_forecasts[self.demand_forecasts['Period'] == target_month]
        logistics_data = self.logistics_data[self.logistics_data['Period'] == target_month]
        
        # 简单优化模型 - 基于利润最大化分配资源
        plan = []
        
        # 对每种商品类型进行分配
        for _, prod_row in production_data.iterrows():
            mine = prod_row['Mine']
            quantity = prod_row['Quantity']
            
            # 获取该商品的价格预测
            commodity_price = price_data[price_data['Commodity'] == mine]['Price'].values[0]
            
            # 按区域需求分配（简化版，实际应更复杂）
            for _, demand_row in demand_data.iterrows():
                region = demand_row['Region']
                demand_qty = min(demand_row['Quantity'], quantity)
                
                if demand_qty > 0:
                    # 计算物流成本
                    route_cost = logistics_data[logistics_data['Route'].str.contains(region)]['Cost'].mean()
                    if np.isnan(route_cost):
                        route_cost = 0
                    
                    # 计算利润
                    profit_per_unit = commodity_price - route_cost
                    
                    # 添加到计划
                    plan.append({
                        'Period': target_month,
                        'Commodity': mine,
                        'Region': region,
                        'Quantity': demand_qty,
                        'Price': commodity_price,
                        'LogisticsCost': route_cost,
                        'ProfitPerUnit': profit_per_unit,
                        'TotalProfit': demand_qty * profit_per_unit,
                        'SalesChannel': self._determine_sales_channel(region)
                    })
                    
                    quantity -= demand_qty
                    if quantity <= 0:
                        break
        
        self.sales_plan = pd.DataFrame(plan)
        return self.sales_plan
    
    def _determine_sales_channel(self, region):
        """根据区域确定销售渠道（简化逻辑）"""
        if 'Asia' in region:
            return 'Asia Sales Team'
        elif 'Europe' in region:
            return 'Europe Sales Team'
        elif 'America' in region:
            return 'America Sales Team'
        else:
            return 'Global Sales Team'
    
    def monitor_sales_progress(self, current_date, completion_threshold=0.9):
        """
        监控销售进度，触发资源再分配
        """
        # 找出即将到期的销售期（截止前3个工作日）
        warning_date = current_date + timedelta(days=3)
        at_risk_sales = self.sales_plan[
            (self.sales_plan['Period'] == warning_date.strftime('%Y-%m')) & 
            (self.sales_plan['CompletionRate'] < completion_threshold)
        ]
        
        return at_risk_sales
    
    def resource_reallocation(self, sales_to_reallocate):
        """
        资源再分配逻辑
        """
        reallocated_resources = []
        
        for _, row in sales_to_reallocate.iterrows():
            # 计算结算基准价（示例逻辑）
            base_price = row['Price'] * 0.95  # 实际应根据市场指数计算
            
            reallocated_resources.append({
                'OriginalSalesChannel': row['SalesChannel'],
                'Commodity': row['Commodity'],
                'Quantity': row['Quantity'] * (1 - row['CompletionRate']),
                'BasePrice': base_price,
                'NewSalesChannel': self._determine_alternative_channel(row['SalesChannel']),
                'ReallocationDate': datetime.now().strftime('%Y-%m-%d')
            })
        
        return pd.DataFrame(reallocated_resources)
    
    def _determine_alternative_channel(self, original_channel):
        """确定替代销售渠道（简化逻辑）"""
        if original_channel == 'Asia Sales Team':
            return 'Global Sales Team'
        elif original_channel == 'Europe Sales Team':
            return 'Global Sales Team'
        elif original_channel == 'America Sales Team':
            return 'Global Sales Team'
        else:
            return 'Special Sales Team'
    
    def calculate_performance(self, sales_data):
        """
        计算渠道绩效指标
        """
        performance = sales_data.groupby('SalesChannel').agg({
            'TotalProfit': 'sum',
            'Quantity': 'sum',
            'CompletionRate': 'mean'
        }).rename(columns={
            'TotalProfit': 'TotalProfit',
            'Quantity': 'TotalVolume',
            'CompletionRate': 'AverageCompletionRate'
        })
        
        performance['PerformanceScore'] = (
            performance['TotalProfit'] * 0.5 + 
            performance['TotalVolume'] * 0.3 +
            performance['AverageCompletionRate'] * 100 * 0.2
        )
        
        return performance.sort_values('PerformanceScore', ascending=False)

# 示例使用
if __name__ == "__main__":
    model = SAndOPModel()
    
    # 添加示例数据
    model.add_price_forecast('2023-11', 'Iron Ore', 120)
    model.add_price_forecast('2023-11', 'Copper', 8500)
    
    model.add_production_plan('2023-11', 'Iron Ore', 10000)
    model.add_production_plan('2023-11', 'Copper', 5000)
    
    model.add_demand_forecast('2023-11', 'Asia', 6000)
    model.add_demand_forecast('2023-11', 'Europe', 3000)
    model.add_demand_forecast('2023-11', 'America', 2000)
    
    model.add_logistics_data('2023-11', 'Asia Route', 15, 10000)
    model.add_logistics_data('2023-11', 'Europe Route', 20, 8000)
    model.add_logistics_data('2023-11', 'America Route', 18, 5000)
    
    # 生成销售计划
    sales_plan = model.generate_sales_plan('2023-11')
    print("生成的销售计划:")
    print(sales_plan)
    
    # 模拟添加完成率数据（实际应从实际销售数据获取）
    sales_plan['CompletionRate'] = np.random.uniform(0.7, 1.0, len(sales_plan))
    
    # 监控销售进度
    at_risk = model.monitor_sales_progress(datetime(2023, 11, 20))
    print("\n需重新分配的销售:")
    print(at_risk)
    
    # 资源再分配
    if not at_risk.empty:
        reallocated = model.resource_reallocation(at_risk)
        print("\n重新分配的资源:")
        print(reallocated)
    
    # 绩效计算
    performance = model.calculate_performance(sales_plan)
    print("\n渠道绩效:")
    print(performance)